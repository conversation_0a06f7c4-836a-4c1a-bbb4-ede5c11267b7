// pages/teacher/layout.tsx (Server Component - NO hooks allowed)
import { TeacherAuthWrapper } from '../../components/authwrappers/TeacherAuthWrapper.client';
import { LayoutWrapper } from '../../components/auth/teacher/dual-sidebar';

const TeacherLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <TeacherAuthWrapper>
      <LayoutWrapper
        showUserNav={true}
        showHeader={true}
      >
        {children}
      </LayoutWrapper>
    </TeacherAuthWrapper>
  );
};

export default TeacherLayout;