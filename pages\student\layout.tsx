// pages/student/layout.tsx (Server Component - NO hooks allowed)
import { StudentAuthWrapper } from '../../components/authwrappers/StudentAuthWrapper.client';
import { LayoutWrapper } from '../../components/auth/student/dual-sidebar';

const StudentLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <StudentAuthWrapper>
      <LayoutWrapper
        showUserNav={true}
        showTopNav={true}
      >
        {children}
      </LayoutWrapper>
    </StudentAuthWrapper>
  );
};

export default StudentLayout;