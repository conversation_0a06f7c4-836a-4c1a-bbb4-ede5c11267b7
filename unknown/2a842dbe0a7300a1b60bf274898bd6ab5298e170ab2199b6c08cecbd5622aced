
// pages/school/layout.tsx (Server Component - NO hooks allowed)
import { SchoolAuthWrapper } from '../../components/authwrappers/SchoolAuthWrapper.client';
import { LayoutWrapper } from '../../components/auth/school/dual-sidebar';

const SchoolLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <SchoolAuthWrapper>
      <LayoutWrapper
        showUserNav={true}
        showHeader={true}
      >
         {children}
      </LayoutWrapper>
      
       
    </SchoolAuthWrapper>
  );
};

export default SchoolLayout;