// components/authwrappers/TeacherAuthWrapper.client.tsx
'use client';

import { useEffect, useRef } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useRedirect, useLocation } from 'blade/hooks';

interface TeacherAuthWrapperProps {
  children: React.ReactNode;
}

export const TeacherAuthWrapper: React.FC<TeacherAuthWrapperProps> = ({ children }) => {
  const { user, _getLoadingState } = useAuth();
  const loading = _getLoadingState();
  const redirect = useRedirect();
  const location = useLocation();
  const urlCleanedRef = useRef(false);
  const logoutRedirectRef = useRef(false);
  
  useEffect(() => {
    // Only redirect when we're sure about auth state
    if (loading) return;

    // Check if this is a fresh login with updated user data
    const urlParams = new URLSearchParams(location.search);
    const isFreshLogin = urlParams.get('fresh_login') === 'true';
    const urlRole = urlParams.get('role');

    // If it's a fresh login and the URL role is teacher, skip redirect checks
    if (isFreshLogin && urlRole === 'teacher') {
      console.log('Fresh login detected for teacher - skipping auth wrapper redirects');

      // Clean up the URL by removing the temporary parameters (only once)
      // Use the user's actual slug to ensure correct URL
      if (user?.slug && !urlCleanedRef.current) {
        const cleanUrl = `/teacher/${user.slug}`;
        console.log('Cleaning URL to:', cleanUrl);
        window.history.replaceState({}, '', cleanUrl);
        urlCleanedRef.current = true;
      }

      return;
    }

    // Redirect if not authenticated
    if (!user) {
      // Check if logout is in progress to avoid interfering with logout redirect
      const logoutInProgress = typeof window !== 'undefined' && sessionStorage.getItem('blade_logout_in_progress');
      console.log('TeacherAuthWrapper - user is null, logoutInProgress:', logoutInProgress, 'pathname:', location.pathname);

      if (logoutInProgress) {
        console.log('TeacherAuthWrapper - logout in progress, skipping redirect');
        // Clear the flag after a longer delay to allow logout redirect to complete
        setTimeout(() => {
          console.log('TeacherAuthWrapper - clearing logout flag');
          sessionStorage.removeItem('blade_logout_in_progress');
        }, 500);
        return;
      }

      // Check if we're already on login page to avoid redirect loops
      if (location.pathname === '/login') {
        console.log('TeacherAuthWrapper - already on login page, not redirecting');
        return;
      }

      // Prevent multiple redirects during logout
      if (!logoutRedirectRef.current) {
        console.log('TeacherAuthWrapper - redirecting to login with teacher role');
        logoutRedirectRef.current = true;
        redirect('/login?role=teacher');
      }
      return;
    }

    // Redirect if wrong role
    if (user.role !== 'teacher') {
      const rolePrefix = user.role === 'school_admin' ? 'school' : user.role;
      redirect(`/${rolePrefix}/${user.slug}`);
      return;
    }
  }, [user, loading, redirect, location]);
  
  // Don't render anything while loading or if user is null/wrong role
  if (loading || !user || user.role !== 'teacher') {
    return null;
  }
  
  return <>{children}</>;
};