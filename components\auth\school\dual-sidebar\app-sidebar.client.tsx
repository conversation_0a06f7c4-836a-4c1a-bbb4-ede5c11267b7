'use client';
import { ComponentProps, useState, useCallback, useRef, useEffect } from "react";
import { motion, AnimatePresence, MotionConfig } from 'motion/react';
import { cn } from "../../../../lib/utils";
import { useIsMobile } from "../../../../hooks/use-mobile";
import { useClickOutside } from "../../../../hooks/use-click-outside";
import { useMeasure } from "../../../../hooks/use-measure";
import { useRedirect, useParams, useLocation } from 'blade/hooks';
import {
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarFooter,
} from "../../../ui/sidebar.client";
import { Sheet, SheetContent, SheetTitle } from "../../../ui/sheet.client";
import { NavMain } from "./nav-main.client";
import { NavProjects } from "./nav-projects.client";
import { NavUser } from "./nav-user.client";
import { TeamSwitcher } from "./team-switcher.client";
import {
  Bell,
  FolderOpen,
  Plus,
  Compass,
  PlayCircle,
  GalleryVerticalEnd,
  SquareTerminal,
  Bot,
  BookOpen,
  Settings2,
  Frame,
  PieChart,
  Map,
} from "lucide-react";
import { Link, Image } from 'blade/client/components';
import { UserNav } from '../shared/dual-sidebar/user-nav.client';


interface AppSidebarProps extends ComponentProps<"div"> {
  flyout: string | null;
  setFlyout: (flyout: string | null) => void;
  variant?: "sidebar" | "floating" | "inset";
  collapsible?: "offcanvas" | "icon" | "none";
}

const iconItems = [
  // Home - Always first in mobile toolbar
  {
    id: "home",
    icon: GalleryVerticalEnd,
    label: "Home",
    tooltip: "Home",
    section: 0,
    url: "" // Empty URL means just /school/[slug]
  },
  // First section - Dashboard
  {
    id: "dashboard",
    icon: SquareTerminal,
    label: "Dashboard",
    tooltip: "Dashboard",
    section: 1,
    url: "dashboard"
  },
  // Second section - Navigation
  {
    id: "analytics",
    icon: PieChart,
    label: "Analytics",
    tooltip: "Analytics",
    section: 2,
    url: "analytics"
  },
  {
    id: "users",
    icon: Bot,
    label: "Users",
    tooltip: "User Management",
    section: 2,
    url: "users"
  },
  {
    id: "classes",
    icon: BookOpen,
    label: "Classes",
    tooltip: "Class Management",
    section: 2,
    url: "classes"
  },
  {
    id: "reports",
    icon: Frame,
    label: "Reports",
    tooltip: "Reports",
    section: 2,
    url: "reports"
  },
  // Third section - Settings
  {
    id: "settings",
    icon: Settings2,
    label: "Settings",
    tooltip: "Settings",
    section: 3,
    url: "settings"
  },
];

function IconButton({ 
  item, 
  isActive, 
  onClick,
  isMobile = false
}: { 
  item: typeof iconItems[number];
  isActive: boolean;
  onClick: () => void;
  isMobile?: boolean;
}) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div className="relative group">
      <button
        onClick={onClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className={cn(
          "relative w-12 h-12 flex items-center justify-center rounded-xl transition-all duration-300 ease-out transform-gpu will-change-transform overflow-hidden",
          "z-20 touch-manipulation select-none outline-none"
        )}
        style={{
          transform: isActive ? 'scale(1.05)' : isHovered ? 'scale(1.02)' : 'scale(1)',
          WebkitTapHighlightColor: 'transparent',
          WebkitTouchCallout: 'none',
          WebkitUserSelect: 'none',
          userSelect: 'none',
          touchAction: 'manipulation',
          cursor: 'pointer',
          pointerEvents: 'auto'
        }}
        type="button"
        aria-pressed={isActive}
        aria-label={item.tooltip}
      >
        {/* Main glassmorphism background */}
        <div 
          className="absolute inset-0 rounded-xl transition-all duration-300 ease-out backdrop-blur-[32px] backdrop-saturate-[180%]"
          style={{
            background: isActive 
              ? `
                linear-gradient(135deg, 
                  rgba(0, 0, 0, 0.12) 0%,
                  rgba(0, 0, 0, 0.08) 30%,
                  rgba(0, 0, 0, 0.04) 70%,
                  rgba(0, 0, 0, 0.02) 100%
                ),
                radial-gradient(circle at 30% 30%, 
                  rgba(0, 0, 0, 0.15) 0%, 
                  transparent 70%
                )
              `
              : isHovered
              ? `
                linear-gradient(135deg, 
                  rgba(0, 0, 0, 0.06) 0%,
                  rgba(0, 0, 0, 0.03) 50%,
                  rgba(0, 0, 0, 0.01) 100%
                )
              `
              : 'transparent',
            border: isActive 
              ? '1px solid rgba(0, 0, 0, 0.1)'
              : isHovered 
              ? '1px solid rgba(0, 0, 0, 0.05)'
              : '1px solid transparent'
          }}
        />
        
        {/* Icon with enhanced styling */}
        <item.icon 
          className={cn(
            "w-5 h-5 relative z-10 transition-all duration-300 ease-out",
            isActive 
              ? "text-blue dark:text-white drop-shadow-[0_2px_8px_rgba(0,0,0,0.4)] dark:drop-shadow-[0_2px_8px_rgba(255,255,255,0.4)] scale-110" 
              : isHovered
              ? "text-black/90 dark:text-white/90 scale-105"
              : "text-black/60 dark:text-white/60"
          )}
        />
      </button>

      {/* Enhanced active indicator */}
      <div 
        className="absolute -right-1 top-[28px] transform -translate-y-1/2 transition-all duration-300 ease-out z-20"
        style={{
          opacity: isActive ? 1 : 0,
          transform: `translateY(-50%) scale(${isActive ? 1 : 0.8})`,
        }}
      >
        <div 
          className="w-1.5 h-1.5 rounded-full bg-black dark:bg-white"
          style={{
            boxShadow: '0 0 12px rgba(0, 0, 0, 0.6), 0 0 24px rgba(0, 0, 0, 0.3)',
            filter: 'blur(0.5px)',
          }}
        />
      </div>
      
      {/* Hover tooltip */}
      <div
        className="absolute left-16 top-[2.5rem] font-manrope_1 transform -translate-y-1/2 bg-gradient-to-b dark:from-[#f8f9fa] dark:via-[#f8f9fa] dark:to-[#e9ecef] from-[#212026] via-[#212026] to-[#29282e] px-3 text-sm leading-8 text-white/80 dark:text-black/80 dark:shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(226_232_240)_inset,0_0.5px_0_1.5px_#64748b_inset] shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] rounded-lg pointer-events-none transition-all duration-300 ease-out whitespace-nowrap z-50"
        style={{
          opacity: isHovered && !isActive ? 1 : 0,
          transform: `translate(${isHovered && !isActive ? '0' : '-8px'}, -50%)`,
          backdropFilter: 'blur(10px)',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        }}
      >
        {item.tooltip}
        <div 
          className="absolute left-0 top-1/2 -translate-x-1 -translate-y-1/2 w-0 h-0 dark:border-r-[#e9ecef] border-r-[#212026]"
          style={{
            borderTop: '6px solid transparent',
            borderBottom: '6px solid transparent',
            borderRightWidth: '6px',
            borderRightStyle: 'solid',
          }}
        />
      </div>
    </div>
  );
}
// Add a Separator component
function Separator() {
  return (
    <div className="w-8 h-px bg-black/10 dark:bg-white/10 my-2 mx-auto" />
  );
}

// Mobile Horizontal Toolbar Component
function MobileHorizontalToolbar({
  flyout,
  setFlyout,
  data,
  handleToggleFlyout
}: {
  flyout: string | null;
  setFlyout: (flyout: string | null) => void;
  data: any;
  handleToggleFlyout: (itemId: string) => void;
}) {
  const [active, setActive] = useState<string | null>(null);
  const [contentRef, { height: heightContent }] = useMeasure();
  const [menuRef, { width: widthContainer }] = useMeasure();
  const ref = useRef<HTMLDivElement>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [maxWidth, setMaxWidth] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Add hooks for navigation
  const redirect = useRedirect();
  const params = useParams();
  const location = useLocation();

  // Function to check if an item is the current page based on URL (mobile version)
  const isCurrentPageMobile = useCallback((item: any): boolean => {
    const pathname = location.pathname;

    // Home page check
    if (item.id === 'home') {
      return !!(pathname.match(/^\/school\/[^\/]+$/) && !pathname.includes('/dashboard') && !pathname.includes('/analytics') && !pathname.includes('/users') && !pathname.includes('/classes'));
    }

    // Other pages check
    if (item.url) {
      return pathname.includes(`/${item.url}`);
    }

    return false;
  }, [location.pathname]);

  // Get all icon items sorted by section
  const toolbarItems = iconItems.sort((a, b) => a.section - b.section);

  useClickOutside(ref as React.RefObject<Element>, () => {
    setIsOpen(false);
    setActive(null);
  });

  // Sync with parent flyout state
  useEffect(() => {
    setActive(flyout);
    setIsOpen(!!flyout);
  }, [flyout]);

  const handleItemClick = (item: any) => {
    // For home item, close any open flyouts and navigate
    if (item.id === 'home') {
      // Close any open flyouts
      setIsOpen(false);
      setActive(null);
      setFlyout(null);

      // Navigate to home
      const slug = Array.isArray(params['slug']) ? params['slug'][0] : params['slug'];
      const fullUrl = `/school/${slug}`;
      redirect(fullUrl);
      return;
    }

    // For other items, toggle flyout
    // Navigation will be handled by Link wrapper (similar to desktop)
    handleToggleFlyout(item.id);
  };

  const transition = { type: "spring", bounce: 0.1, duration: 0.4 };

  return (
    <MotionConfig transition={transition}>
      <div className="fixed bottom-2 left-1/2 w-[96vw] transform -translate-x-1/2 z-[100000004]" ref={ref}>
        <div className={cn(
          "h-full w-full bg-gradient-to-b from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]",
          isOpen ? "rounded-xl" : "rounded-full"
        )}>
          <div className={cn(
            "overflow-hidden",
            isOpen ? "rounded-xl" : "rounded-full"
          )}>
            <AnimatePresence>
              {isOpen ? (
                <motion.div
                  ref={contentRef}
                  initial={{ height: 0 }}
                  animate={{ height: heightContent || "auto" }}
                  exit={{ height: 0 }}
                  className="overflow-hidden"
                >
                  <div className="p-2 max-h-[80vh] overflow-y-auto">
                    {renderFlyoutContent(flyout, data)}
                  </div>
                </motion.div>
              ) : null}
            </AnimatePresence>
          </div>

          {/* Horizontal toolbar with main nav and user nav */}
          <div className="relative flex">
            {/* Main navigation section (scrollable) */}
            <div className="flex-1 relative">
              {/* Left blur gradient */}
              <div className={cn(
                "absolute left-0 top-0 bottom-0 w-4 z-10 pointer-events-none bg-gradient-to-r from-zinc-100 via-zinc-150 to-transparent dark:from-[#212026] dark:via-[#25242a] dark:to-transparent",
                isOpen ? "rounded-l-xl" : "rounded-l-full"
              )} />

              {/* Right blur gradient for main nav */}
              <div className={cn(
                "absolute right-0 top-0 bottom-0 w-4 z-10 pointer-events-none bg-gradient-to-l from-zinc-100 via-zinc-150 to-transparent dark:from-[#212026] dark:via-[#25242a] dark:to-transparent",
                isOpen ? "rounded-none" : "rounded-none"
              )} />

              <div
                ref={scrollContainerRef}
                className="flex space-x-2 p-2 pr-1 overflow-x-auto [&::-webkit-scrollbar]:hidden"
                style={{
                  scrollbarWidth: 'none',
                  msOverflowStyle: 'none',
                } as React.CSSProperties}
              >
                <div ref={menuRef} className="flex space-x-2 min-w-max">
                  {toolbarItems.map((item) => {
                    const isCurrentPage = isCurrentPageMobile(item);

                    const buttonElement = (
                      <button
                        key={item.id}
                        aria-label={item.label}
                        className={cn(
                          'relative flex h-9 w-9 shrink-0 scale-100 select-none appearance-none items-center justify-center rounded-full transition-colors hover:bg-zinc-100 dark:hover:bg-zinc-800 focus-visible:ring-2 active:scale-[0.98]',
                          active === item.id
                            ? 'bg-gradient-to-br from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]'
                            : isCurrentPage
                            ? 'text-zinc-900 dark:text-zinc-100'
                            : 'text-zinc-500 dark:text-zinc-400 hover:text-zinc-800 dark:hover:text-zinc-200'
                        )}
                        type='button'
                        onClick={() => handleItemClick(item)}
                      >
                        <item.icon
                          className={cn(
                            "h-5 w-5 transition-colors",
                            active === item.id
                              ? "text-zinc-800 dark:text-zinc-200"
                              : isCurrentPage
                              ? "text-black dark:text-white"
                              : "text-zinc-500 dark:text-zinc-400"
                          )}
                        />
                      </button>
                    );

                    // If item has URL, wrap in Link for navigation
                    if (item.url) {
                      const slug = Array.isArray(params['slug']) ? params['slug'][0] : params['slug'];
                      const fullUrl = item.url.startsWith('/')
                        ? item.url
                        : `/school/${slug}/${item.url}`;

                      return (
                        <Link key={item.id} href={fullUrl}>
                          <a className="block">
                            {buttonElement}
                          </a>
                        </Link>
                      );
                    }

                    // If no URL, return button without Link wrapper
                    return buttonElement;
                  })}
                </div>
              </div>

              {/* User navigation section (fixed on right) */}
              <div className="flex-shrink-0 pl-1 pr-2 py-2 flex items-center">
                <UserNav className="scale-90" inMobileToolbar={true} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </MotionConfig>
  );
}

export function AppSidebar({
  flyout,
  setFlyout,
  variant = "sidebar",
  collapsible = "none",
  className,
  ...props
}: AppSidebarProps) {
  const [activeItem, setActiveItem] = useState<string | null>("projects");
  const isMobile = useIsMobile();

  // Simplified toggle handler without debouncing - matches working right-sidebar pattern
  const handleToggleFlyout = useCallback((itemId: string) => {
    console.log(`🎯 Toggle flyout for ${itemId}:`, { currentFlyout: flyout, itemId });
    const newFlyout = flyout === itemId ? null : itemId;
    console.log(`🎯 Setting flyout to:`, newFlyout);
    setFlyout(newFlyout);
  }, [flyout, setFlyout]);

  const data = {
    user: {
      name: "School Admin",
      email: "<EMAIL>",
      avatar: "/avatars/admin.jpg",
    },
    teams: [
      {
        name: "School District",
        logo: GalleryVerticalEnd,
        plan: "Education",
      },
    ],
    navMain: [
      {
        title: "Dashboard",
        url: "#",
        icon: SquareTerminal,
        isActive: true,
        items: [
          {
            title: "Overview",
            url: "#",
          },
          {
            title: "Analytics",
            url: "#",
          },
          {
            title: "Reports",
            url: "#",
          },
        ],
      },
      {
        title: "Students",
        url: "#",
        icon: Bot,
        items: [
          {
            title: "All Students",
            url: "#",
          },
          {
            title: "Enrollment",
            url: "#",
          },
          {
            title: "Attendance",
            url: "#",
          },
        ],
      },
      {
        title: "Teachers",
        url: "#",
        icon: BookOpen,
        items: [
          {
            title: "All Teachers",
            url: "#",
          },
          {
            title: "Schedules",
            url: "#",
          },
          {
            title: "Performance",
            url: "#",
          },
        ],
      },
      {
        title: "Settings",
        url: "#",
        icon: Settings2,
        items: [
          {
            title: "General",
            url: "#",
          },
          {
            title: "Users",
            url: "#",
          },
          {
            title: "Security",
            url: "#",
          },
        ],
      },
    ],
    projects: [
      {
        name: "Academic Year 2024",
        url: "#",
        icon: Frame,
      },
      {
        name: "Summer Programs",
        url: "#",
        icon: PieChart,
      },
      {
        name: "Extracurriculars",
        url: "#",
        icon: Map,
      },
    ],
  };

  // Mobile vs Desktop rendering
  return (
    <div className="flex h-full">
      {isMobile ? (
        <>
          {/* Mobile Horizontal Toolbar */}
          <MobileHorizontalToolbar
            flyout={flyout}
            setFlyout={setFlyout}
            data={data}
            handleToggleFlyout={handleToggleFlyout}
          />
        </>
      ) : (
        /* Desktop Layout */
        <>
        {/* Icon Rail */}
        <div className="flex flex-col w-18 h-svh z-30 fixed left-0 top-0 border-r border-black/10 dark:border-white/10 bg-[#f2f2f2] dark:bg-[#0d0d0d]">
          <div className="flex flex-col items-center gap-2 p-2">
            {/* Logo Section */}
            <div className="w-10 h-10 flex items-center justify-center  z-20">
              <Image
                    src="/logo-lightmode.png"
                    alt="Penned Logo"
                    className="w-6 h-6 dark:hidden"
                    width={28}
                    height={28}
                  />
                  {/* Dark mode logo - shows in dark mode */}
                  <Image
                    src="/logo-darkmode.png"
                    alt="Penned Logo"
                    className="w-6 h-6 hidden dark:block"
                    width={28}
                    height={28}
                  />
                            
                            </div>

            {/* Bell Icon */}
            <IconButton
              item={iconItems[0]}
              isActive={flyout === iconItems[0].id}
              onClick={() => handleToggleFlyout(iconItems[0].id)}
              isMobile={isMobile}
            />
            
            <Separator />
            
            {/* Navigation Section */}
            {iconItems
              .filter(item => item.section === 2)
              .map((item) => (
                <IconButton
                  key={item.id}
                  item={item}
                  isActive={flyout === item.id}
                  onClick={() => handleToggleFlyout(item.id)}
                  isMobile={isMobile}
                />
              ))}
            
            <Separator />
            
            {/* Create New Section */}
            {iconItems
              .filter(item => item.section === 3)
              .map((item) => (
                <IconButton
                  key={item.id}
                  item={item}
                  isActive={flyout === item.id}
                  onClick={() => handleToggleFlyout(item.id)}
                  isMobile={isMobile}
                />
              ))}
          </div>
        </div>

        {/* Flyout Panel */}
        {isMobile ? (
          <Sheet open={!!flyout} onOpenChange={(open) => !open && setFlyout(null)} modal={false}>
            <SheetContent
              side="left"
              className={cn(
                "mobile-sheet-content",
                "border-r  border-black/5 dark:border-white/5 p-0 bg-[#f2f2f2] dark:bg-[#0d0d0d]",
                "fixed left-[72px] top-0 h-full w-[calc(100vw-72px)] max-w-none",
                "z-10"
              )}
            >
              <SheetTitle className="sr-only">
                {flyout === 'projects' ? 'Projects' :
                 flyout === 'runs' ? 'Runs' :
                 flyout === 'discover' ? 'Discover' :
                 flyout === 'new' ? 'Create New' :
                 flyout === 'notifications' ? 'Notifications' :
                 'Navigation Panel'}
              </SheetTitle>
              <div className="h-full overflow-hidden">
                {renderFlyoutContent(flyout, data)}
              </div>
            </SheetContent>
          </Sheet>
        ) : (
          <>
            {/* Flyout separator line */}
            <AnimatePresence>
              {flyout && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ type: "spring", stiffness: 300, damping: 30, mass: 0.8 }}
                  className="fixed left-[71px] top-0 h-svh w-[1px] bg-[#f2f2f2] dark:bg-[#0d0d0d] z-10"
                />
              )}
            </AnimatePresence>

            {/* Flyout content panel */}
            <AnimatePresence mode="wait">
              {flyout && (
                <motion.div
                  initial={{ opacity: 0, x: -100 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -100 }}
                  transition={{ type: "spring", stiffness: 300, damping: 30, mass: 0.8 }}
                  className="fixed left-[72px] top-0 h-svh w-[278px] bg-[#f2f2f2] dark:bg-[#0d0d0d] border-r border-black/5 dark:border-white/5 shadow-xl z-10 flex flex-col"
                >
                  {renderFlyoutContent(flyout, data)}
                </motion.div>
              )}
            </AnimatePresence>
          </>
        )}
      )}
    </div>
  );
}

function renderFlyoutContent(flyout: string | null, data: any) {
  if (!flyout) return null;
  
  switch (flyout) {
    case "notifications":
      return (
        <div className="flex py-2 flex-col h-full">
          <div className="p-2 border-b border-black/20 dark:border-white/20">
            <h2 className="text-xl text-left font-semibold text-black dark:text-white">
              Notifications
            </h2>
          </div>
          <div className="flex-1 p-4 overflow-auto">
            <div className="space-y-2">
              <div className="p-3 rounded-md bg-sidebar-accent/50 hover:bg-sidebar-accent cursor-pointer">
                <p className="text-sm font-medium">New student enrollment</p>
                <p className="text-xs text-muted-foreground">2 hours ago</p>
              </div>
              <div className="p-3 rounded-md bg-sidebar-accent/50 hover:bg-sidebar-accent cursor-pointer">
                <p className="text-sm font-medium">Teacher schedule update</p>
                <p className="text-xs text-muted-foreground">Yesterday</p>
              </div>
            </div>
          </div>
        </div>
      );
    
    case "runs":
      return (
        <div className="flex py-2 flex-col h-full">
      <div className="p-2 border-b border-black/20 dark:border-white/20">
            <h2 className="text-xl text-left font-semibold text-black dark:text-white">
              Recent Activities
            </h2>          
          </div>
          <div className="flex-1 p-4 overflow-auto">
            <div className="space-y-2">
              <div className="p-3 rounded-md bg-sidebar-accent/50 hover:bg-sidebar-accent cursor-pointer">
                <p className="text-sm font-medium">Grade Reports Generated</p>
              </div>
              <div className="p-3 rounded-md bg-sidebar-accent/50 hover:bg-sidebar-accent cursor-pointer">
                <p className="text-sm font-medium">Attendance Sync</p>
              </div>
            </div>
          </div>
        </div>
      );

    case "discover":
      return (
        <div className="flex py-2 flex-col h-full">
         <div className="p-2 border-b border-black/20 dark:border-white/20">
            <h2 className="text-xl text-left font-semibold text-black dark:text-white">
              Discover
            </h2>          
          </div>
          <div className="flex-1 p-4 overflow-auto">
            <div className="space-y-2">
              <div className="p-3 rounded-md bg-sidebar-accent/50 hover:bg-sidebar-accent cursor-pointer">
                <p className="text-sm font-medium">New Features</p>
              </div>
              <div className="p-3 rounded-md bg-sidebar-accent/50 hover:bg-sidebar-accent cursor-pointer">
                <p className="text-sm font-medium">Training Resources</p>
              </div>
            </div>
          </div>
        </div>
      );

    case "new":
      return (
        <div className="flex py-2 flex-col h-full">
          <div className="p-2 border-b border-black/20 dark:border-white/20">
            <h2 className="text-xl text-left font-semibold text-black dark:text-white">
              Create New  
            </h2>          
          </div>
          <div className="flex-1 p-4 overflow-auto">
            <div className="space-y-2">
              <button className="w-full text-left p-2 rounded-md hover:bg-sidebar-accent">
                New Student
              </button>
              <button className="w-full text-left p-2 rounded-md hover:bg-sidebar-accent">
                New Teacher
              </button>
              <button className="w-full text-left p-2 rounded-md hover:bg-sidebar-accent">
                New Class
              </button>
            </div>
          </div>
        </div>
      );

    case "projects":
      return (
        <div className="flex py-2 flex-col h-full">
          <div className="p-4 border-b border-black/20 dark:border-white/20">
            <h2 className="text-xl text-left font-semibold text-black dark:text-white">
              Projects
            </h2>
          </div>
          <div className="flex-1 overflow-auto">
            <NavProjects
              projects={data.projects}
              showCreateButton={true}
              limit={20}
            />
          </div>
        </div>
      );

    default:
      return (
        <div className="flex items-center justify-center h-full">
          <p className="text-muted-foreground">Select a panel</p>
        </div>
      );
  }
}