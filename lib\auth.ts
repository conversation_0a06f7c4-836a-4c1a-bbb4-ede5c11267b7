// lib/auth.ts - Unified Better Auth configuration
import { betterAuth } from "better-auth"
import { ronin } from "blade-better-auth"
import { emailOTP, admin, organization, username } from "better-auth/plugins"
import { createSyntaxFactory } from 'ronin';
import type { BetterAuthOptions } from "better-auth";

// Get environment variables - Blade 0.9.3+ handles server/client context automatically
const RONIN_TOKEN = process.env["BLADE_RONIN_TOKEN"] || '';
const BETTER_AUTH_SECRET = process.env["BLADE_BETTER_AUTH_SECRET"] || '';
const BETTER_AUTH_URL = process.env["BLADE_BETTER_AUTH_URL"] || 'http://localhost:3000';
const GOOGLE_CLIENT_ID = process.env["BLADE_GOOGLE_CLIENT_ID"] || '';
const GOOGLE_CLIENT_SECRET = process.env["BLADE_GOOGLE_CLIENT_SECRET"] || '';
const RESEND_API_KEY = process.env["BLADE_RESEND_API_KEY"] || '';

// Validate required environment variables
if (!BETTER_AUTH_SECRET) {
  console.warn('BETTER_AUTH_SECRET is not set, using default for development');
}

if (!RONIN_TOKEN) {
  console.warn('RONIN_TOKEN is not set, this may cause database connection issues');
}

// Create RONIN client for use in hooks
const client = createSyntaxFactory({
  token: RONIN_TOKEN,
});

// Helper function to create social provider config only if credentials are available
const createSocialProviders = () => {
  const providers: any = {};

  if (GOOGLE_CLIENT_ID && GOOGLE_CLIENT_SECRET) {
    providers.google = {
      clientId: GOOGLE_CLIENT_ID,
      clientSecret: GOOGLE_CLIENT_SECRET,
    };
  }

  return providers;
};

export const auth = betterAuth({
  database: ronin(client),
  secret: BETTER_AUTH_SECRET || 'dev-secret-change-in-production',
  baseURL: BETTER_AUTH_URL,
  socialProviders: createSocialProviders(),
  // Add session configuration to potentially fix the headers issue
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
    cookieCache: {
      enabled: true,
      maxAge: 5 * 60 // 5 minutes cache
    }
  },
  plugins: [
    // Username plugin for students
    username({
      // Students can't sign up themselves - only sign in with username created by teacher
    }),
    // Email OTP plugin for teachers and school admins
    emailOTP({
      // Allow automatic user registration during OTP sign-in
      disableSignUp: false,
      sendVerificationOTP: async ({ email, otp, type }: {
        email: string;
        otp: string;
        type: "sign-in" | "email-verification" | "forget-password"
      }) => {
        try {
          // Use improved resend validation
          const { resend, validateEmailConfig } = await import("./resend");
          
          const emailValidation = validateEmailConfig();
          if (!emailValidation.isValid) {
            console.error("Email configuration error:", emailValidation.error);
            throw new Error(`Email service not configured: ${emailValidation.error}`);
          }
          
          const result = await resend.emails.send({
            from: "Penned <<EMAIL>>",
            to: email,
            subject: type === "sign-in" ? "Your Sign-in Code" : "Verify Your Email",
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>Penned - Verification Code</h2>
                <p>Use this code to ${type === "sign-in" ? "sign in" : "verify your email"}:</p>
                <div style="background: #f4f4f4; padding: 20px; border-radius: 8px; font-size: 24px; font-weight: bold; text-align: center; margin: 20px 0;">
                  ${otp}
                </div>
                <p style="color: #666;">This code will expire in 10 minutes.</p>
                <p style="color: #666; font-size: 12px;">This email was sent to verify your identity.</p>
              </div>
            `
          });

          if (result.error) {
            console.error("Resend API error:", result.error);
            throw new Error(`Failed to send email: ${result.error.message || 'Unknown error'}`);
          }

          console.log("OTP email sent successfully to:", email);
        } catch (error) {
          console.error("Error sending OTP email:", error);
          if (error instanceof Error) {
            throw new Error(`Email service error: ${error.message}`);
          } else {
            throw new Error("Failed to send verification email");
          }
        }
      },
      otpLength: 6,
      expiresIn: 60 * 10, // 10 minutes
    }),
    // Admin plugin for teachers and school admins
    admin({
      defaultRole: "user", // Default role for regular users
      roleField: "role"
    }),
    // Organization plugin for schools
    organization({
      allowUserToCreateOrganization: true,
      sendInvitationEmail: async (data) => {
        try {
          const { resend, validateEmailConfig } = await import("./resend");
          
          const emailValidation = validateEmailConfig();
          if (!emailValidation.isValid) {
            console.error("Email configuration error:", emailValidation.error);
            throw new Error(`Email service not configured: ${emailValidation.error}`);
          }
          
          // Construct the invitation link using the invitation ID
          const inviteLink = `${BETTER_AUTH_URL}/organization/invite/${data.id}`;
          
          const result = await resend.emails.send({
            from: "Penned <<EMAIL>>",
            to: data.email,
            subject: `Invitation to join ${data.organization.name}`,
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>You've been invited to join ${data.organization.name}</h2>
                <p>You have been invited to join <strong>${data.organization.name}</strong> as a <strong>${data.role}</strong>.</p>
                <p>Click the link below to accept the invitation:</p>
                <a href="${inviteLink}" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                  Accept Invitation
                </a>
                <p style="color: #666; margin-top: 20px;">This invitation will expire in 7 days.</p>
                <p style="color: #666; font-size: 12px;">If you're not expecting this invitation, you can safely ignore this email.</p>
              </div>
            `
          });

          if (result.error) {
            console.error("Resend API error for invitation:", result.error);
            throw new Error(`Failed to send invitation email: ${result.error.message || 'Unknown error'}`);
          }

          console.log("Invitation email sent successfully to:", data.email);
        } catch (error) {
          console.error("Error sending invitation email:", error);
          if (error instanceof Error) {
            throw new Error(`Invitation email service error: ${error.message}`);
          } else {
            throw new Error("Failed to send invitation email");
          }
        }
      }
    })
  ],
  // User configuration with role-based fields
  user: {
    additionalFields: {
      role: {
        type: "string",
        required: true,
        defaultValue: "student"
      },
      slug: {
        type: "string",
        required: true
      },
      // Student fields
      teacherId: {
        type: "string",
        required: false
      },
      isActive: {
        type: "boolean",
        required: false,
        defaultValue: false
      },
      classId: {
        type: "string",
        required: false
      },
      grade: {
        type: "string",
        required: false
      },
      // Teacher fields
      isIndependent: {
        type: "boolean",
        required: false,
        defaultValue: true
      },
      schoolId: {
        type: "string",
        required: false
      },
      department: {
        type: "string",
        required: false
      },
      subjects: {
        type: "string", // JSON string of array
        required: false
      },
      isVerified: {
        type: "boolean",
        required: false,
        defaultValue: false
      },
      // School admin fields
      schoolName: {
        type: "string",
        required: false
      },
      schoolAddress: {
        type: "string",
        required: false
      },
      schoolPlaceId: {
        type: "string",
        required: false
      },
      schoolType: {
        type: "string",
        required: false
      },
      schoolDistrict: {
        type: "string",
        required: false
      },
      studentCount: {
        type: "number",
        required: false
      },
      teacherCount: {
        type: "number",
        required: false
      }
    }
  },
  // Enable email/password for teachers and school admins
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false, // Set to true in production
    autoSignIn: true
  },

} satisfies BetterAuthOptions);

// Helper functions (to be implemented with your database logic)
async function updateUserProfile(userId: string, updates: any): Promise<void> {
  // Implement database update for user profile
  // This should update your RONIN database
  console.log('Updating user profile:', userId, updates);
}

async function handleGoogleSignup(user: any): Promise<void> {
  // Handle Google OAuth specific logic
  // Auto-verify teachers who sign up with Google
  const slug = user.email?.split('@')[0].toLowerCase().replace(/[^a-z0-9-]/g, '') || 'user';
  await updateUserProfile(user.id, {
    isVerified: true,
    slug,
    role: user.role || 'teacher'
  });
}

export type AuthType = {
  Variables: {
    user: typeof auth.$Infer.Session.user | null
    session: typeof auth.$Infer.Session.session | null
  }
}