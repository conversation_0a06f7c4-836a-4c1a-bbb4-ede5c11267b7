// components/RouteProtectionWrapper.client.tsx
'use client';

import { useRouteProtection, routeConfig } from '../utils/route-protection';
import { useLocation } from 'blade/hooks';

interface RouteProtectionWrapperProps {
  children: React.ReactNode;
}

export const RouteProtectionWrapper: React.FC<RouteProtectionWrapperProps> = ({ children }) => {
  const { user, loading } = useRouteProtection();
  const location = useLocation();

  // Find the current route configuration
  const currentPath = location.pathname;
  const matchedRoute = routeConfig
    .sort((a, b) => b.path.length - a.path.length)
    .find(route => currentPath.startsWith(route.path));

  // If still loading auth state, don't render anything to prevent flash
  if (loading) {
    return null;
  }

  // Check if this is a fresh login in progress
  const urlParams = new URLSearchParams(location.search);
  const isFreshLogin = urlParams.get('fresh_login') === 'true';

  // For public routes that should redirect authenticated users (/ and /login)
  if (matchedRoute?.type === 'public' && user && (currentPath === '/' || currentPath === '/login')) {
    // Don't render public content for authenticated users - they'll be redirected
    return null;
  }

  // For protected routes without authentication
  if (matchedRoute?.type === 'protected' && !user) {
    // If it's a fresh login, allow rendering while session establishes
    if (isFreshLogin) {
      return <>{children}</>;
    }
    // Don't render protected content for unauthenticated users - they'll be redirected
    return null;
  }

  // For protected routes with wrong role
  if (matchedRoute?.type === 'protected' && user && matchedRoute.allowedRoles && !matchedRoute.allowedRoles.includes(user.role)) {
    // Don't render content for users with wrong role - they'll be redirected
    return null;
  }

  // Safe to render content
  return <>{children}</>;
};